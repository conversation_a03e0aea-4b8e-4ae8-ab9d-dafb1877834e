<script setup lang="ts">
import { <PERSON><PERSON>, Card, CardContent, CardHeader, Checkbox, Input, Label } from '@billing/ui'
import { AlertCircle, Eye, EyeOff, Loader2, Lock, Mail, Shield } from 'lucide-vue-next'
import { computed, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { authApi } from '@/api/auth'
import { useUserStore } from '@/store/user'

const router = useRouter()
const userStore = useUserStore()

const form = reactive({
  email: '',
  password: '',
  remember: false,
})

const loading = ref(false)
const error = ref('')
const showPassword = ref(false)
const emailFocused = ref(false)
const passwordFocused = ref(false)

// 表单验证状态
const isFormValid = computed(() => {
  return form.email.trim() !== '' && form.password.trim() !== '' && form.email.includes('@')
})

const emailError = computed(() => {
  if (!form.email)
    return ''
  if (!form.email.includes('@'))
    return '请输入有效的邮箱地址'
  return ''
})

function togglePasswordVisibility() {
  showPassword.value = !showPassword.value
}

async function handleLogin() {
  if (!isFormValid.value)
    return

  loading.value = true
  error.value = ''

  try {
    const response = await authApi.login({
      email: form.email,
      password: form.password,
    })

    // 假设返回的数据包含token和user信息
    if (response.token) {
      userStore.setToken(response.token)

      // 如果选择记住登录状态，保存到localStorage
      if (form.remember) {
        localStorage.setItem('auth_token', response.token)
      }

      // 跳转到主页
      await router.push('/dashboard')
    }
  }
  catch (err: any) {
    console.error('登录失败:', err)
    error.value = err.message || '登录失败，请检查邮箱和密码'
  }
  finally {
    loading.value = false
  }
}
</script>

<template>
  <div class="min-h-screen flex items-center justify-center gradient-bg-light py-8 px-4 sm:px-6 lg:px-8">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-primary/5 rounded-full blur-3xl" />
      <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-primary/5 rounded-full blur-3xl" />
    </div>

    <div class="relative w-full max-w-md">
      <Card class="card-shadow card-hover border-0 backdrop-blur-sm bg-card/80 bg-blur">
        <CardHeader class="text-center pb-6">
          <!-- Logo/图标 -->
          <div class="logo-container mx-auto w-16 h-16 bg-primary/10 rounded-2xl flex items-center justify-center mb-4 transition-all duration-300 hover:scale-105">
            <Shield class="w-8 h-8 text-primary" />
          </div>

          <h1 class="text-2xl font-bold text-foreground mb-2">
            欢迎回来
          </h1>
          <p class="text-muted-foreground text-sm">
            登录计费系统管理端
          </p>
        </CardHeader>

        <CardContent class="space-y-6">
          <form
            class="space-y-4"
            @submit.prevent="handleLogin"
          >
            <!-- 邮箱输入框 -->
            <div class="space-y-2">
              <Label
                for="email"
                class="text-sm font-medium text-foreground"
              >
                邮箱地址
              </Label>
              <div class="relative group focus-scale input-glow">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail class="h-4 w-4 text-muted-foreground group-focus-within:text-primary transition-colors" />
                </div>
                <Input
                  id="email"
                  v-model="form.email"
                  type="email"
                  autocomplete="email"
                  placeholder="请输入邮箱地址"
                  :disabled="loading"
                  class="pl-10 h-11 transition-all duration-200 focus:ring-2 focus:ring-primary/20"
                  :class="{
                    'input-error': emailError && form.email,
                    'input-success': !emailError && form.email && form.email.includes('@'),
                    'loading-pulse': loading,
                  }"
                  @focus="emailFocused = true"
                  @blur="emailFocused = false"
                />
              </div>
              <div
                v-if="emailError && form.email"
                class="text-xs text-destructive flex items-center gap-1"
              >
                <AlertCircle class="h-3 w-3" />
                {{ emailError }}
              </div>
            </div>

            <!-- 密码输入框 -->
            <div class="space-y-2">
              <Label
                for="password"
                class="text-sm font-medium text-foreground"
              >
                密码
              </Label>
              <div class="relative group focus-scale input-glow">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock class="h-4 w-4 text-muted-foreground group-focus-within:text-primary transition-colors" />
                </div>
                <Input
                  id="password"
                  v-model="form.password"
                  :type="showPassword ? 'text' : 'password'"
                  autocomplete="current-password"
                  placeholder="请输入密码"
                  :disabled="loading"
                  class="pl-10 pr-10 h-11 transition-all duration-200 focus:ring-2 focus:ring-primary/20"
                  :class="{
                    'loading-pulse': loading,
                  }"
                  @focus="passwordFocused = true"
                  @blur="passwordFocused = false"
                />
                <button
                  type="button"
                  class="absolute inset-y-0 right-0 pr-3 flex items-center text-muted-foreground hover:text-foreground transition-colors hover:scale-110"
                  :disabled="loading"
                  @click="togglePasswordVisibility"
                >
                  <Eye
                    v-if="!showPassword"
                    class="h-4 w-4"
                  />
                  <EyeOff
                    v-else
                    class="h-4 w-4"
                  />
                </button>
              </div>
            </div>

            <!-- 记住登录 -->
            <div class="flex items-center justify-between pt-2">
              <div class="flex items-center space-x-2">
                <Checkbox
                  id="remember"
                  v-model="form.remember"
                  :disabled="loading"
                  class="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                />
                <Label
                  for="remember"
                  class="text-sm text-muted-foreground cursor-pointer select-none"
                >
                  记住登录状态
                </Label>
              </div>
            </div>

            <!-- 错误提示 -->
            <div
              v-if="error"
              class="error-shake rounded-lg bg-destructive/10 border border-destructive/20 p-3 animate-in slide-in-from-top-2 duration-300"
            >
              <div class="flex items-start gap-2">
                <AlertCircle class="h-4 w-4 text-destructive mt-0.5 flex-shrink-0" />
                <div class="flex-1">
                  <p class="text-sm font-medium text-destructive">
                    登录失败
                  </p>
                  <p class="text-xs text-destructive/80 mt-1">
                    {{ error }}
                  </p>
                </div>
              </div>
            </div>

            <!-- 登录按钮 -->
            <Button
              type="submit"
              size="lg"
              class="btn-ripple w-full h-11 font-medium transition-all duration-200 hover:scale-[1.02] active:scale-[0.98]"
              :disabled="loading || !isFormValid"
              :class="{
                'opacity-50 cursor-not-allowed': !isFormValid,
                'bg-primary hover:bg-primary/90': isFormValid,
                'loading-pulse': loading,
              }"
            >
              <Loader2
                v-if="loading"
                class="w-4 h-4 mr-2 animate-spin"
              />
              <span>{{ loading ? '登录中...' : '登录' }}</span>
            </Button>
          </form>
        </CardContent>
      </Card>

      <!-- 底部信息 -->
      <div class="mt-6 text-center">
        <p class="text-xs text-muted-foreground">
          © 2024 计费系统. 保留所有权利.
        </p>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 自定义动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(var(--primary), 0.4);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(var(--primary), 0);
  }
}

/* Logo 悬浮动画 */
.logo-container {
  animation: float 6s ease-in-out infinite;
}

/* 输入框聚焦时的发光效果 */
.input-glow:focus-within {
  animation: pulse-glow 2s infinite;
}

/* 按钮点击波纹效果 */
.btn-ripple {
  position: relative;
  overflow: hidden;
}

.btn-ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn-ripple:active::before {
  width: 300px;
  height: 300px;
}

/* 响应式设计优化 */
@media (max-width: 640px) {
  .login-container {
    padding: 1rem;
  }

  .login-card {
    margin: 0;
    border-radius: 0;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
}

/* 暗色模式下的特殊效果 */
.dark .bg-blur {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* 加载状态的脉冲效果 */
.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

/* 错误提示的摇摆动画 */
.error-shake {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

/* 成功状态的绿色边框 */
.input-success {
  border-color: rgb(34 197 94);
  box-shadow: 0 0 0 3px rgb(34 197 94 / 0.1);
}

/* 错误状态的红色边框 */
.input-error {
  border-color: rgb(239 68 68);
  box-shadow: 0 0 0 3px rgb(239 68 68 / 0.1);
}

/* 平滑过渡效果 */
* {
  transition: all 0.2s ease-in-out;
}

/* 聚焦时的缩放效果 */
.focus-scale:focus-within {
  transform: scale(1.02);
}

/* 卡片悬浮效果 */
.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04);
}
</style>
